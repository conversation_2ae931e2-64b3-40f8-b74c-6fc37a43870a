import rateLimit from 'express-rate-limit';
import helmet from 'helmet';
import { NODE_ENV } from '../config/env.js';
import cacheService from '../services/cache.service.js';
import { sendResponse } from '../utils/responseHandler.js';
import { RateLimitError } from './error-handler.middleware.js';

/**
 * Enhanced Security Middleware Collection
 */
class SecurityMiddleware {
  /**
   * Advanced rate limiting with Redis backend
   */
  static createRateLimit(options = {}) {
    const defaultOptions = {
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 100, // limit each IP to 100 requests per windowMs
      message: 'Too many requests from this IP, please try again later',
      standardHeaders: true,
      legacyHeaders: false,
      skipSuccessfulRequests: false,
      skipFailedRequests: false,
      keyGenerator: (req) => {
        // Use user ID if authenticated, otherwise IP
        return req.user?.userId || req.ip;
      },
      handler: (req, res) => {
        throw new RateLimitError('Rate limit exceeded');
      },
      ...options
    };

    // Use Redis store if available
    if (cacheService.isEnabled) {
      defaultOptions.store = {
        incr: async (key) => {
          const result = await cacheService.increment(key);
          if (result === 1) {
            await cacheService.set(key, result, Math.ceil(defaultOptions.windowMs / 1000));
          }
          return { totalHits: result, resetTime: new Date(Date.now() + defaultOptions.windowMs) };
        },
        decrement: async (key) => {
          await cacheService.increment(key, -1);
        },
        resetKey: async (key) => {
          await cacheService.del(key);
        }
      };
    }

    return rateLimit(defaultOptions);
  }

  /**
   * Strict rate limiting for authentication endpoints
   */
  static authRateLimit = this.createRateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // 5 attempts per 15 minutes
    message: 'Too many authentication attempts, please try again later',
    skipSuccessfulRequests: true
  });

  /**
   * API rate limiting for general endpoints
   */
  static apiRateLimit = this.createRateLimit({
    windowMs: 60 * 1000, // 1 minute
    max: 60, // 60 requests per minute
    message: 'API rate limit exceeded'
  });

  /**
   * Strict rate limiting for sensitive operations
   */
  static sensitiveRateLimit = this.createRateLimit({
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 10, // 10 requests per hour
    message: 'Rate limit exceeded for sensitive operations'
  });

  /**
   * Enhanced helmet configuration
   */
  static securityHeaders = helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
        fontSrc: ["'self'", "https://fonts.gstatic.com"],
        imgSrc: ["'self'", "data:", "https:"],
        scriptSrc: NODE_ENV === 'development' 
          ? ["'self'", "'unsafe-eval'", "'unsafe-inline'"]
          : ["'self'"],
        connectSrc: ["'self'"],
        frameSrc: ["'none'"],
        objectSrc: ["'none'"],
        upgradeInsecureRequests: NODE_ENV === 'production' ? [] : null
      }
    },
    crossOriginEmbedderPolicy: false, // Disable for API
    hsts: {
      maxAge: 31536000,
      includeSubDomains: true,
      preload: true
    }
  });

  /**
   * Request size limiting
   */
  static requestSizeLimit = (req, res, next) => {
    const maxSize = 10 * 1024 * 1024; // 10MB
    
    if (req.headers['content-length'] && parseInt(req.headers['content-length']) > maxSize) {
      return sendResponse(res, 413, false, 'Request entity too large');
    }
    
    next();
  };

  /**
   * IP whitelist/blacklist middleware
   */
  static ipFilter(options = {}) {
    const { whitelist = [], blacklist = [] } = options;
    
    return (req, res, next) => {
      const clientIP = req.ip || req.connection.remoteAddress;
      
      // Check blacklist first
      if (blacklist.length > 0 && blacklist.includes(clientIP)) {
        return sendResponse(res, 403, false, 'Access denied from this IP');
      }
      
      // Check whitelist if provided
      if (whitelist.length > 0 && !whitelist.includes(clientIP)) {
        return sendResponse(res, 403, false, 'IP not whitelisted');
      }
      
      next();
    };
  }

  /**
   * User agent validation
   */
  static validateUserAgent = (req, res, next) => {
    const userAgent = req.get('User-Agent');
    
    if (!userAgent) {
      return sendResponse(res, 400, false, 'User-Agent header required');
    }
    
    // Block known bad user agents
    const blockedAgents = [
      /bot/i,
      /crawler/i,
      /spider/i,
      /scraper/i
    ];
    
    const isBlocked = blockedAgents.some(pattern => pattern.test(userAgent));
    
    if (isBlocked && NODE_ENV === 'production') {
      return sendResponse(res, 403, false, 'Access denied');
    }
    
    next();
  };

  /**
   * Request method validation
   */
  static allowedMethods(methods = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH']) {
    return (req, res, next) => {
      if (!methods.includes(req.method)) {
        return sendResponse(res, 405, false, 'Method not allowed');
      }
      next();
    };
  }

  /**
   * Content type validation
   */
  static validateContentType = (req, res, next) => {
    if (['POST', 'PUT', 'PATCH'].includes(req.method)) {
      const contentType = req.get('Content-Type');
      
      if (!contentType) {
        return sendResponse(res, 400, false, 'Content-Type header required');
      }
      
      const allowedTypes = [
        'application/json',
        'application/x-www-form-urlencoded',
        'multipart/form-data'
      ];
      
      const isValidType = allowedTypes.some(type => contentType.includes(type));
      
      if (!isValidType) {
        return sendResponse(res, 415, false, 'Unsupported media type');
      }
    }
    
    next();
  };

  /**
   * Request timeout middleware
   */
  static requestTimeout(timeout = 30000) {
    return (req, res, next) => {
      req.setTimeout(timeout, () => {
        if (!res.headersSent) {
          sendResponse(res, 408, false, 'Request timeout');
        }
      });
      next();
    };
  }

  /**
   * Suspicious activity detection
   */
  static suspiciousActivityDetection = async (req, res, next) => {
    try {
      const clientIP = req.ip;
      const userAgent = req.get('User-Agent');
      const key = `suspicious:${clientIP}`;
      
      // Track request patterns
      const activity = await cacheService.get(key) || {
        requests: 0,
        lastRequest: Date.now(),
        userAgents: new Set(),
        endpoints: new Set()
      };
      
      activity.requests++;
      activity.userAgents.add(userAgent);
      activity.endpoints.add(req.path);
      
      // Detect suspicious patterns
      const timeDiff = Date.now() - activity.lastRequest;
      const isSuspicious = (
        activity.requests > 100 || // Too many requests
        activity.userAgents.size > 5 || // Multiple user agents
        timeDiff < 100 // Too fast requests
      );
      
      if (isSuspicious && NODE_ENV === 'production') {
        // Log suspicious activity
        console.warn(`🚨 Suspicious activity detected from ${clientIP}`);
        
        // Temporarily block
        await cacheService.set(`blocked:${clientIP}`, true, 3600); // 1 hour
        return sendResponse(res, 429, false, 'Suspicious activity detected');
      }
      
      activity.lastRequest = Date.now();
      await cacheService.set(key, activity, 3600); // 1 hour
      
      next();
    } catch (error) {
      console.error('Suspicious activity detection error:', error);
      next(); // Continue on error
    }
  };

  /**
   * API key validation for external integrations
   */
  static validateApiKey = async (req, res, next) => {
    try {
      const apiKey = req.headers['x-api-key'];
      
      if (!apiKey) {
        return sendResponse(res, 401, false, 'API key required');
      }
      
      // Validate API key format
      if (!/^[a-zA-Z0-9]{32,}$/.test(apiKey)) {
        return sendResponse(res, 401, false, 'Invalid API key format');
      }
      
      // Check if API key is valid (implement your logic)
      const isValid = await this.checkApiKeyValidity(apiKey);
      
      if (!isValid) {
        return sendResponse(res, 401, false, 'Invalid API key');
      }
      
      req.apiKey = apiKey;
      next();
    } catch (error) {
      console.error('API key validation error:', error);
      sendResponse(res, 500, false, 'API key validation failed');
    }
  };

  /**
   * Check API key validity (placeholder implementation)
   */
  static async checkApiKeyValidity(apiKey) {
    // Implement your API key validation logic
    // This could check against database, cache, or external service
    return apiKey === process.env.MASTER_API_KEY;
  }

  /**
   * CORS configuration for different environments
   */
  static corsOptions = {
    origin: (origin, callback) => {
      const allowedOrigins = NODE_ENV === 'production'
        ? process.env.ALLOWED_ORIGINS?.split(',') || []
        : ['http://localhost:3000', 'http://localhost:5173', 'http://localhost:8080'];
      
      if (!origin || allowedOrigins.includes(origin)) {
        callback(null, true);
      } else {
        callback(new Error('Not allowed by CORS'));
      }
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: [
      'Origin',
      'X-Requested-With',
      'Content-Type',
      'Accept',
      'Authorization',
      'X-API-Key',
      'X-App-Key',
      'X-Hash-Key',
      'X-Timestamp',
      'X-Signature'
    ],
    exposedHeaders: ['X-Total-Count', 'X-Rate-Limit-Remaining'],
    maxAge: 86400 // 24 hours
  };

  /**
   * Security headers for API responses
   */
  static securityResponseHeaders = (req, res, next) => {
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('X-Frame-Options', 'DENY');
    res.setHeader('X-XSS-Protection', '1; mode=block');
    res.setHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
    res.setHeader('Permissions-Policy', 'geolocation=(), microphone=(), camera=()');
    
    if (NODE_ENV === 'production') {
      res.setHeader('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
    }
    
    next();
  };
}

export default SecurityMiddleware;
export { SecurityMiddleware };
