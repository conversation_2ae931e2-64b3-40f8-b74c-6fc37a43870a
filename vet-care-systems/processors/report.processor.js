import PDFDocument from 'pdfkit';
import fs from 'fs';
import path from 'path';
import Appointment from '../models/appointment.model.js';
import Client from '../models/client.model.js';
import Pet from '../models/pet.model.js';
import Invoice from '../models/invoice.model.js';
import { NODE_ENV } from '../config/env.js';

class ReportProcessor {
  constructor() {
    this.reportsDir = path.join(process.cwd(), 'reports');
    this.ensureReportsDirectory();
  }

  ensureReportsDirectory() {
    if (!fs.existsSync(this.reportsDir)) {
      fs.mkdirSync(this.reportsDir, { recursive: true });
    }
  }

  async generateReport(job) {
    const { type, clinicId, dateRange, format, options } = job.data;

    try {
      let reportData;
      let filename;

      switch (type) {
        case 'appointments':
          reportData = await this.generateAppointmentsReport(clinicId, dateRange);
          filename = `appointments_${clinicId}_${Date.now()}.${format}`;
          break;
        case 'revenue':
          reportData = await this.generateRevenueReport(clinicId, dateRange);
          filename = `revenue_${clinicId}_${Date.now()}.${format}`;
          break;
        case 'clients':
          reportData = await this.generateClientsReport(clinicId, dateRange);
          filename = `clients_${clinicId}_${Date.now()}.${format}`;
          break;
        case 'inventory':
          reportData = await this.generateInventoryReport(clinicId);
          filename = `inventory_${clinicId}_${Date.now()}.${format}`;
          break;
        default:
          throw new Error(`Unknown report type: ${type}`);
      }

      const filePath = path.join(this.reportsDir, filename);

      if (format === 'pdf') {
        await this.generatePDFReport(reportData, filePath, type);
      } else if (format === 'csv') {
        await this.generateCSVReport(reportData, filePath);
      } else {
        throw new Error(`Unsupported format: ${format}`);
      }

      console.log(`📊 Report generated: ${filename}`);

      return {
        status: 'generated',
        reportId: `report_${Date.now()}`,
        filename,
        filePath,
        type,
        format,
        generatedAt: new Date().toISOString()
      };
    } catch (error) {
      console.error('Report generation error:', error);
      throw error;
    }
  }

  async generateAppointmentsReport(clinicId, dateRange) {
    const { startDate, endDate } = dateRange;

    const appointments = await Appointment.find({
      clinicId,
      appointmentDate: {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      }
    })
    .populate('clientId', 'name phone email')
    .populate('petId', 'name species breed')
    .sort({ appointmentDate: -1 })
    .lean();

    const summary = {
      totalAppointments: appointments.length,
      completedAppointments: appointments.filter(a => a.status === 'completed').length,
      cancelledAppointments: appointments.filter(a => a.status === 'cancelled').length,
      scheduledAppointments: appointments.filter(a => a.status === 'scheduled').length,
      totalRevenue: appointments
        .filter(a => a.status === 'completed')
        .reduce((sum, a) => sum + (a.totalAmount || 0), 0)
    };

    return {
      title: 'Appointments Report',
      period: `${startDate} to ${endDate}`,
      clinicId,
      summary,
      appointments,
      generatedAt: new Date().toISOString()
    };
  }

  async generateRevenueReport(clinicId, dateRange) {
    const { startDate, endDate } = dateRange;

    const invoices = await Invoice.find({
      clinicId,
      createdAt: {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      }
    })
    .populate('appointmentId')
    .populate('clientId', 'name')
    .sort({ createdAt: -1 })
    .lean();

    const summary = {
      totalInvoices: invoices.length,
      totalRevenue: invoices.reduce((sum, inv) => sum + inv.totalAmount, 0),
      paidInvoices: invoices.filter(inv => inv.status === 'paid').length,
      pendingInvoices: invoices.filter(inv => inv.status === 'pending').length,
      averageInvoiceAmount: invoices.length > 0 
        ? invoices.reduce((sum, inv) => sum + inv.totalAmount, 0) / invoices.length 
        : 0
    };

    // Group by service categories
    const categoryBreakdown = {};
    invoices.forEach(invoice => {
      if (invoice.appointmentId && invoice.appointmentId.appointmentCategories) {
        invoice.appointmentId.appointmentCategories.forEach(category => {
          if (!categoryBreakdown[category.name]) {
            categoryBreakdown[category.name] = { count: 0, revenue: 0 };
          }
          categoryBreakdown[category.name].count++;
          categoryBreakdown[category.name].revenue += category.totalCharge || 0;
        });
      }
    });

    return {
      title: 'Revenue Report',
      period: `${startDate} to ${endDate}`,
      clinicId,
      summary,
      categoryBreakdown,
      invoices,
      generatedAt: new Date().toISOString()
    };
  }

  async generateClientsReport(clinicId, dateRange) {
    const { startDate, endDate } = dateRange;

    const clients = await Client.find({
      clinicId,
      createdAt: {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      }
    })
    .populate('pets')
    .sort({ createdAt: -1 })
    .lean();

    const summary = {
      totalClients: clients.length,
      newClients: clients.length,
      totalPets: clients.reduce((sum, client) => sum + (client.pets?.length || 0), 0),
      averagePetsPerClient: clients.length > 0 
        ? clients.reduce((sum, client) => sum + (client.pets?.length || 0), 0) / clients.length 
        : 0
    };

    return {
      title: 'Clients Report',
      period: `${startDate} to ${endDate}`,
      clinicId,
      summary,
      clients,
      generatedAt: new Date().toISOString()
    };
  }

  async generateInventoryReport(clinicId) {
    // This would integrate with your inventory system
    // For now, returning a placeholder structure
    return {
      title: 'Inventory Report',
      clinicId,
      summary: {
        totalItems: 0,
        lowStockItems: 0,
        outOfStockItems: 0,
        totalValue: 0
      },
      items: [],
      generatedAt: new Date().toISOString()
    };
  }

  async generatePDFReport(reportData, filePath, type) {
    const doc = new PDFDocument();
    doc.pipe(fs.createWriteStream(filePath));

    // Header
    doc.fontSize(20).text(reportData.title, 50, 50);
    doc.fontSize(12).text(`Period: ${reportData.period}`, 50, 80);
    doc.fontSize(12).text(`Generated: ${reportData.generatedAt}`, 50, 100);

    // Summary section
    doc.fontSize(16).text('Summary', 50, 140);
    let yPosition = 160;

    Object.entries(reportData.summary).forEach(([key, value]) => {
      const label = key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
      doc.fontSize(12).text(`${label}: ${value}`, 50, yPosition);
      yPosition += 20;
    });

    // Data section
    if (type === 'appointments' && reportData.appointments) {
      doc.addPage();
      doc.fontSize(16).text('Appointments', 50, 50);
      yPosition = 80;

      reportData.appointments.slice(0, 20).forEach(appointment => {
        if (yPosition > 700) {
          doc.addPage();
          yPosition = 50;
        }
        
        doc.fontSize(10)
           .text(`${appointment.appointmentDate} - ${appointment.clientId?.name} - ${appointment.petId?.name}`, 50, yPosition);
        yPosition += 15;
      });
    }

    doc.end();
  }

  async generateCSVReport(reportData, filePath) {
    let csvContent = '';

    if (reportData.appointments) {
      // CSV for appointments
      csvContent = 'Date,Client,Pet,Status,Amount\n';
      reportData.appointments.forEach(appointment => {
        csvContent += `${appointment.appointmentDate},${appointment.clientId?.name || ''},${appointment.petId?.name || ''},${appointment.status},${appointment.totalAmount || 0}\n`;
      });
    } else if (reportData.invoices) {
      // CSV for revenue
      csvContent = 'Date,Client,Invoice ID,Amount,Status\n';
      reportData.invoices.forEach(invoice => {
        csvContent += `${invoice.createdAt},${invoice.clientId?.name || ''},${invoice.invoiceId},${invoice.totalAmount},${invoice.status}\n`;
      });
    }

    fs.writeFileSync(filePath, csvContent);
  }

  // Health check
  async healthCheck() {
    try {
      // Check if reports directory is writable
      const testFile = path.join(this.reportsDir, 'test.txt');
      fs.writeFileSync(testFile, 'test');
      fs.unlinkSync(testFile);

      return {
        status: 'healthy',
        reportsDirectory: this.reportsDir,
        writable: true
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message
      };
    }
  }
}

// Create singleton instance
const reportProcessor = new ReportProcessor();

export default reportProcessor;
export { reportProcessor };
