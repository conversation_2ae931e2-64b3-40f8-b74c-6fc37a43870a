import express from "express";
import { createServer } from "http";
import { PORT, NODE_ENV } from "./config/env.js";

// Import new services
import cacheService from "./services/cache.service.js";
import queueService from "./services/queue.service.js";
import healthService from "./services/health.service.js";

// Import enhanced middleware
import { error<PERSON><PERSON><PERSON>, notFoundHandler, globalExceptionHandler } from "./middleware/error-handler.middleware.js";
import SecurityMiddleware from "./middleware/security.middleware.js";

// Import versioned routes
import v1Routes from "./routes/v1/index.js";

// Import legacy routes (for backward compatibility)
import authRouter from "./routes/auth.routes.js";
import usersRouter from "./routes/user.routes.js";
import staffRouter from "./routes/staff.routes.js";
import clinicsRouter from "./routes/clinic.routes.js";
import appointmentRoutes from "./routes/appointment.routes.js";
import serviceCategoryRoutes from "./routes/serviceCategory.routes.js";
import appointmentCategoryRoutes from "./routes/appointmentCategory.routes.js";
import serviceRouter from "./routes/service.routes.js";
import clientsRouter from "./routes/client.routes.js";
import petsRouter from "./routes/pet.routes.js";
import speciesRouter from "./routes/species.routes.js";
import breedRouter from "./routes/breed.routes.js";
import rolesRouter from "./routes/role.routes.js";
import permissionsRouter from "./routes/permission.routes.js";

import clientClinicRelationshipRouter from './routes/clientClinicRelationship.routes.js';
import petClinicRelationshipRouter from './routes/petClinicRelationship.routes.js';
import healthRecordRouter from './routes/healthRecord.routes.js';
import inventoryRouter from './routes/inventory.routes.js';
import medicationDispensingRouter from './routes/medicationDispensing.routes.js';
import invoiceRouter from './routes/invoice.routes.js';
import paymentRouter from './routes/payment.routes.js';
import receiptRouter from './routes/receipt.routes.js';

import workFlowRouter from "./routes/workflow.routes.js";
import aiRouter from "./routes/ai.routes.js";
import connectToDatabase from "./database/mongodb.js";
import { seedDefaultRoles } from "./seeders/defaultRoles.js";
import { seedDefaultCategories } from "./seeders/defaultCategories.js";
import { seedAppointmentCategoryServices } from "./seeders/appointmentCategoryServices.js";
import './models/index.js'; // Import all models
import {
  verifyToken,
  arcjetMiddleware,
  errorMiddleware,
  csrfProtection,
  handleCSRFError,
  provideCSRFToken
} from "./middlewares/index.js";
import { validationMiddleware } from "./middlewares/validation.middleware.js";
import { auditMiddleware } from "./utils/auditLogger.js";
import { initializeDefaultCounters } from "./utils/counterManager.js";
import webSocketService from "./utils/websocketService.js";
import cookieParser from "cookie-parser";
import cors from "cors";
import helmet from "helmet";

const app = express();
const server = createServer(app);

// Initialize WebSocket service
webSocketService.initialize(server);

// Initialize global exception handlers
globalExceptionHandler();

// Enhanced security middleware
app.use(SecurityMiddleware.securityHeaders);
app.use(SecurityMiddleware.securityResponseHeaders);
app.use(SecurityMiddleware.requestSizeLimit);
app.use(SecurityMiddleware.validateContentType);
app.use(SecurityMiddleware.requestTimeout(30000)); // 30 second timeout

// Basic middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: false, limit: '10mb' }));
app.use(cookieParser());

// Enable Arcjet in production for security
if (NODE_ENV === 'production') {
  app.use(arcjetMiddleware);
  app.use(SecurityMiddleware.suspiciousActivityDetection);
}

// Audit logging middleware (temporarily disabled due to MongoDB buffering issues)
// app.use(auditMiddleware({
//   excludePaths: ['/health', '/metrics', '/api/test'],
//   excludeMethods: ['GET'],
//   logBody: NODE_ENV === 'development',
//   logResponse: false
// }));

// Enhanced CORS configuration
app.use(cors(SecurityMiddleware.corsOptions));

// CSRF protection for non-GET routes (only when needed)
const csrfProtectedRoutes = express.Router();
csrfProtectedRoutes.use(csrfProtection);
csrfProtectedRoutes.use(handleCSRFError); // Handle CSRF errors only for protected routes
csrfProtectedRoutes.use(provideCSRFToken);

// Import admin routes
import adminRoutes from './routes/admin.routes.js';
import clinicCategorySettingsRoutes from './routes/clinicCategorySettings.routes.js';

// API Routes - Versioned and organized
console.log('🔧 Mounting API v1 routes...');
app.use('/api/v1', SecurityMiddleware.apiRateLimit, v1Routes);
console.log('✅ API v1 routes mounted!');

// Legacy API routes (for backward compatibility)
console.log('🔧 Mounting legacy routes...');
app.use('/api/auth', SecurityMiddleware.authRateLimit, authRouter);
app.use('/api/users', usersRouter);
app.use('/api/staff', staffRouter);
app.use('/api/clinics', clinicsRouter);
app.use('/api/appointments', appointmentRoutes);
app.use('/api/service-categories', serviceCategoryRoutes);
app.use('/api/categories', appointmentCategoryRoutes);
app.use('/api/clinic-category-settings', clinicCategorySettingsRoutes);
app.use('/api/services', serviceRouter);
app.use('/api/clients', clientsRouter);
app.use('/api/pets', petsRouter);
app.use('/api/species', speciesRouter);
app.use('/api/breeds', breedRouter);
app.use('/api/roles', rolesRouter);
app.use('/api/permissions', permissionsRouter);
app.use('/api/workflows', workFlowRouter);
app.use('/api/ai', aiRouter);
app.use('/api/health-records', healthRecordRouter);
app.use('/api/client-clinic-relationships', clientClinicRelationshipRouter);
app.use('/api/pet-clinic-relationships', petClinicRelationshipRouter);
app.use('/api/inventory', inventoryRouter);
app.use('/api/medication-dispensing', medicationDispensingRouter);
app.use('/api/invoices', invoiceRouter);
app.use('/api/payments', paymentRouter);
app.use('/api/receipts', receiptRouter);
app.use('/api/admin', adminRoutes);
console.log('✅ Legacy routes mounted!');

app.get("/", (_req, res) => {
    res.send("Hello World!");
})

// Test route to verify routing is working
app.get("/api/test", (_req, res) => {
    res.json({ message: "Test route works!" });
})

// Force re-seed roles (for development/testing)
app.post('/api/seed-roles', async (req, res) => {
    try {
        await seedDefaultRoles(true); // Force re-seed
        res.json({
            success: true,
            message: 'Roles re-seeded successfully'
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to seed roles',
            error: error.message
        });
    }
});

// Check existing roles (for debugging)
app.get('/api/check-roles', async (req, res) => {
    try {
        const Role = mongoose.model('Role');
        const roles = await Role.find({}).select('roleId roleName category description').lean();
        res.json({
            success: true,
            count: roles.length,
            roles: roles
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to fetch roles',
            error: error.message
        });
    }
});

// 404 handler for unmatched routes
app.use(notFoundHandler);

// Enhanced error handling middleware should be last
app.use(errorHandler);

server.listen(PORT, async () => {
    console.log(`🚀 Server started on port http://localhost:${PORT}`);
    console.log(`🔌 WebSocket server initialized`);
    console.log(`🌍 Environment: ${NODE_ENV}`);

    try {
        // Initialize database
        await connectToDatabase();
        console.log('📊 Database connected successfully');

        // Initialize Redis cache service
        await cacheService.initialize();
        console.log('🔴 Cache service initialized');

        // Initialize queue service
        await queueService.initialize();
        console.log('📋 Queue service initialized');

        // Initialize default counters for auto-incrementing IDs
        await initializeDefaultCounters();
        console.log('🔢 Default counters initialized');

        // Seed default data
        await seedDefaultRoles();
        console.log('👥 Default roles seeded');

        await seedDefaultCategories();
        console.log('📋 Default categories seeded');

        await seedAppointmentCategoryServices();
        console.log('🏥 Appointment category services seeded');

        // Health check endpoint
        console.log(`🏥 Health check available at http://localhost:${PORT}/api/v1/health`);
        console.log(`📊 Metrics available at http://localhost:${PORT}/api/v1/health/metrics`);

        console.log('✅ Server initialization complete!');
    } catch (error) {
        console.error('❌ Server initialization failed:', error);
        process.exit(1);
    }
});
