import express from "express";
import { createServer } from "http";
import { PORT, NODE_ENV } from "./config/env.js";
import authRouter from "./routes/auth.routes.js";
import usersRouter from "./routes/user.routes.js";
import staffRouter from "./routes/staff.routes.js";
import clinicsRouter from "./routes/clinic.routes.js";
import appointmentRoutes from "./routes/appointment.routes.js";
import serviceCategoryRoutes from "./routes/serviceCategory.routes.js";
import appointmentCategoryRoutes from "./routes/appointmentCategory.routes.js";
import serviceRouter from "./routes/service.routes.js";
import clientsRouter from "./routes/client.routes.js";
import petsRouter from "./routes/pet.routes.js";
import speciesRouter from "./routes/species.routes.js";
import breedRouter from "./routes/breed.routes.js";
import rolesRouter from "./routes/role.routes.js";
import permissionsRouter from "./routes/permission.routes.js";

import clientClinicRelationshipRouter from './routes/clientClinicRelationship.routes.js';
import petClinicRelationshipRouter from './routes/petClinicRelationship.routes.js';
import healthRecordRouter from './routes/healthRecord.routes.js';
import inventoryRouter from './routes/inventory.routes.js';
import medicationDispensingRouter from './routes/medicationDispensing.routes.js';
import invoiceRouter from './routes/invoice.routes.js';
import paymentRouter from './routes/payment.routes.js';
import receiptRouter from './routes/receipt.routes.js';

import workFlowRouter from "./routes/workflow.routes.js";
import aiRouter from "./routes/ai.routes.js";
import connectToDatabase from "./database/mongodb.js";
import { seedDefaultRoles } from "./seeders/defaultRoles.js";
import { seedDefaultCategories } from "./seeders/defaultCategories.js";
import { seedAppointmentCategoryServices } from "./seeders/appointmentCategoryServices.js";
import './models/index.js'; // Import all models
import {
  verifyToken,
  arcjetMiddleware,
  errorMiddleware,
  csrfProtection,
  handleCSRFError,
  provideCSRFToken
} from "./middlewares/index.js";
import { validationMiddleware } from "./middlewares/validation.middleware.js";
import { auditMiddleware } from "./utils/auditLogger.js";
import { initializeDefaultCounters } from "./utils/counterManager.js";
import webSocketService from "./utils/websocketService.js";
import cookieParser from "cookie-parser";
import cors from "cors";
import helmet from "helmet";

const app = express();
const server = createServer(app);

// Initialize WebSocket service
webSocketService.initialize(server);

// Security middleware
app.use(helmet()); // Add security headers
app.use(express.json({ limit: '1mb' })); // Limit request size
app.use(express.urlencoded({ extended: false }));
app.use(cookieParser());

// Enable Arcjet in production for security
if (NODE_ENV === 'production') {
  app.use(arcjetMiddleware);
}

// Audit logging middleware (temporarily disabled due to MongoDB buffering issues)
// app.use(auditMiddleware({
//   excludePaths: ['/health', '/metrics', '/api/test'],
//   excludeMethods: ['GET'],
//   logBody: NODE_ENV === 'development',
//   logResponse: false
// }));

// Allow requests from your frontend (React)
app.use(cors({
    origin: ["http://localhost:8080", "http://localhost:5173"], // Support both Vite and other dev servers
    methods: "GET,HEAD,PUT,PATCH,POST,DELETE",
    credentials: true // If using cookies or authentication headers
}));

// CSRF protection for non-GET routes (only when needed)
const csrfProtectedRoutes = express.Router();
csrfProtectedRoutes.use(csrfProtection);
csrfProtectedRoutes.use(handleCSRFError); // Handle CSRF errors only for protected routes
csrfProtectedRoutes.use(provideCSRFToken);

// Import admin routes
import adminRoutes from './routes/admin.routes.js';
import clinicCategorySettingsRoutes from './routes/clinicCategorySettings.routes.js';

// API Routes - Clean and organized
console.log('🔧 Mounting auth routes...');
app.use('/api/auth', authRouter);
console.log('✅ Auth routes mounted!');
app.use('/api/users', usersRouter);
app.use('/api/staff', staffRouter);
app.use('/api/clinics', clinicsRouter);
console.log('🔧 Mounting appointment routes...');
app.use('/api/appointments', appointmentRoutes);
app.use('/api/service-categories', serviceCategoryRoutes);
app.use('/api/categories', appointmentCategoryRoutes);
app.use('/api/clinic-category-settings', clinicCategorySettingsRoutes);
console.log('✅ Appointment routes mounted!');
app.use('/api/services', serviceRouter);
app.use('/api/clients', clientsRouter);
app.use('/api/pets', petsRouter);
app.use('/api/species', speciesRouter);
app.use('/api/breeds', breedRouter);
app.use('/api/roles', rolesRouter);
app.use('/api/permissions', permissionsRouter);
app.use('/api/workflows', workFlowRouter);
app.use('/api/ai', aiRouter);

app.use('/api/health-records', healthRecordRouter);
app.use('/api/client-clinic-relationships', clientClinicRelationshipRouter);
app.use('/api/pet-clinic-relationships', petClinicRelationshipRouter);
app.use('/api/inventory', inventoryRouter);
app.use('/api/medication-dispensing', medicationDispensingRouter);
app.use('/api/invoices', invoiceRouter);
app.use('/api/payments', paymentRouter);
app.use('/api/receipts', receiptRouter);
app.use('/api/admin', adminRoutes);

app.get("/", (_req, res) => {
    res.send("Hello World!");
})

// Test route to verify routing is working
app.get("/api/test", (_req, res) => {
    res.json({ message: "Test route works!" });
})

// Force re-seed roles (for development/testing)
app.post('/api/seed-roles', async (req, res) => {
    try {
        await seedDefaultRoles(true); // Force re-seed
        res.json({
            success: true,
            message: 'Roles re-seeded successfully'
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to seed roles',
            error: error.message
        });
    }
});

// Check existing roles (for debugging)
app.get('/api/check-roles', async (req, res) => {
    try {
        const Role = mongoose.model('Role');
        const roles = await Role.find({}).select('roleId roleName category description').lean();
        res.json({
            success: true,
            count: roles.length,
            roles: roles
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            message: 'Failed to fetch roles',
            error: error.message
        });
    }
});

// Error middleware should be last
app.use(errorMiddleware);

server.listen(PORT, async () => {
    console.log(`🚀 Server started on port http://localhost:${PORT}`);
    console.log(`🔌 WebSocket server initialized`);

    try {
        await connectToDatabase();
        console.log('📊 Database connected successfully');

        // Initialize default counters for auto-incrementing IDs
        await initializeDefaultCounters();
        console.log('🔢 Default counters initialized');

        await seedDefaultRoles();
        console.log('👥 Default roles seeded');

        await seedDefaultCategories();
        console.log('📋 Default categories seeded');

        await seedAppointmentCategoryServices();
        console.log('🏥 Appointment category services seeded');

        console.log('✅ Server initialization complete!');
    } catch (error) {
        console.error('❌ Server initialization failed:', error);
        process.exit(1);
    }
});
