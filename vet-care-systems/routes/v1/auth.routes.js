import express from 'express';
import authService from '../../services/auth.service.js';
import { sendResponse } from '../../utils/responseHandler.js';
import { validationMiddleware } from '../../middlewares/validation.middleware.js';
import EnhancedAuthMiddleware from '../../middleware/enhanced-auth.middleware.js';

const router = express.Router();

/**
 * @route POST /api/v1/auth/login
 * @desc User/Staff login with enhanced security
 * @access Public
 */
router.post('/login', validationMiddleware('login'), async (req, res) => {
  try {
    const { email, password, otpCode, userType, rememberMe } = req.body;
    
    const result = await authService.login(email, password, otpCode, userType);
    
    if (result.requiresOtp) {
      return sendResponse(res, 200, true, 'OTP required', {
        requiresOtp: true,
        userId: result.userId
      });
    }

    // Set secure cookie if remember me is enabled
    if (rememberMe) {
      res.cookie('refreshToken', result.token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60 * 1000 // 7 days
      });
    }

    sendResponse(res, 200, true, 'Login successful', {
      token: result.token,
      user: result.user,
      expiresIn: '1h'
    });
  } catch (error) {
    console.error('Login error:', error);
    sendResponse(res, 401, false, error.message);
  }
});

/**
 * @route POST /api/v1/auth/verify-otp
 * @desc Verify OTP for two-factor authentication
 * @access Public
 */
router.post('/verify-otp', validationMiddleware('verifyOtp'), async (req, res) => {
  try {
    const { email, password, otpCode, userType } = req.body;
    
    const result = await authService.login(email, password, otpCode, userType);
    
    sendResponse(res, 200, true, 'OTP verified successfully', {
      token: result.token,
      user: result.user
    });
  } catch (error) {
    console.error('OTP verification error:', error);
    sendResponse(res, 401, false, error.message);
  }
});

/**
 * @route POST /api/v1/auth/refresh
 * @desc Refresh JWT token
 * @access Private
 */
router.post('/refresh', async (req, res) => {
  try {
    const { refreshToken } = req.body;
    const cookieToken = req.cookies.refreshToken;
    
    const token = refreshToken || cookieToken;
    
    if (!token) {
      return sendResponse(res, 401, false, 'Refresh token required');
    }

    const result = await authService.refreshToken(token);
    
    sendResponse(res, 200, true, 'Token refreshed successfully', {
      token: result.token,
      expiresIn: '1h'
    });
  } catch (error) {
    console.error('Token refresh error:', error);
    sendResponse(res, 401, false, error.message);
  }
});

/**
 * @route POST /api/v1/auth/logout
 * @desc Logout user and invalidate session
 * @access Private
 */
router.post('/logout', EnhancedAuthMiddleware.authenticateAndAuthorize, async (req, res) => {
  try {
    await authService.logout(req.user.userId);
    
    // Clear refresh token cookie
    res.clearCookie('refreshToken');
    
    sendResponse(res, 200, true, 'Logout successful');
  } catch (error) {
    console.error('Logout error:', error);
    sendResponse(res, 500, false, 'Logout failed');
  }
});

/**
 * @route POST /api/v1/auth/forgot-password
 * @desc Send password reset email
 * @access Public
 */
router.post('/forgot-password', validationMiddleware('forgotPassword'), async (req, res) => {
  try {
    const { email } = req.body;
    
    // Implementation for password reset
    // This would typically generate a reset token and send email
    
    sendResponse(res, 200, true, 'Password reset email sent');
  } catch (error) {
    console.error('Forgot password error:', error);
    sendResponse(res, 500, false, 'Failed to send reset email');
  }
});

/**
 * @route POST /api/v1/auth/reset-password
 * @desc Reset password with token
 * @access Public
 */
router.post('/reset-password', validationMiddleware('resetPassword'), async (req, res) => {
  try {
    const { token, newPassword } = req.body;
    
    // Implementation for password reset
    // This would verify the reset token and update password
    
    sendResponse(res, 200, true, 'Password reset successful');
  } catch (error) {
    console.error('Reset password error:', error);
    sendResponse(res, 400, false, error.message);
  }
});

/**
 * @route POST /api/v1/auth/change-password
 * @desc Change user password
 * @access Private
 */
router.post('/change-password', 
  EnhancedAuthMiddleware.authenticateAndAuthorize,
  validationMiddleware('changePassword'),
  async (req, res) => {
    try {
      const { currentPassword, newPassword } = req.body;
      
      // Implementation for password change
      // This would verify current password and update to new password
      
      sendResponse(res, 200, true, 'Password changed successfully');
    } catch (error) {
      console.error('Change password error:', error);
      sendResponse(res, 400, false, error.message);
    }
  }
);

/**
 * @route GET /api/v1/auth/me
 * @desc Get current user profile
 * @access Private
 */
router.get('/me', EnhancedAuthMiddleware.authenticateAndAuthorize, async (req, res) => {
  try {
    const session = await authService.validateSession(req.user.userId);
    
    if (!session) {
      return sendResponse(res, 401, false, 'Session invalid');
    }

    sendResponse(res, 200, true, 'User profile retrieved', {
      user: {
        id: session.userId,
        email: session.email,
        name: session.name,
        userType: session.userType,
        role: session.role,
        clinicId: session.clinicId,
        isClinicOwner: session.isClinicOwner,
        permissions: session.permissions
      },
      session: {
        loginTime: session.loginTime,
        lastActivity: session.lastActivity
      }
    });
  } catch (error) {
    console.error('Get profile error:', error);
    sendResponse(res, 500, false, 'Failed to get user profile');
  }
});

/**
 * @route POST /api/v1/auth/validate-session
 * @desc Validate current session
 * @access Private
 */
router.post('/validate-session', EnhancedAuthMiddleware.authenticateAndAuthorize, async (req, res) => {
  try {
    sendResponse(res, 200, true, 'Session valid', {
      valid: true,
      user: {
        id: req.user.userId,
        email: req.user.email,
        userType: req.user.userType,
        role: req.user.roleName
      }
    });
  } catch (error) {
    console.error('Session validation error:', error);
    sendResponse(res, 401, false, 'Session invalid');
  }
});

/**
 * @route GET /api/v1/auth/permissions
 * @desc Get user permissions
 * @access Private
 */
router.get('/permissions', EnhancedAuthMiddleware.authenticateAndAuthorize, async (req, res) => {
  try {
    const permissions = await authService.getUserPermissions(
      req.user.userId,
      req.user.userType === 'staff'
    );

    sendResponse(res, 200, true, 'Permissions retrieved', {
      permissions
    });
  } catch (error) {
    console.error('Get permissions error:', error);
    sendResponse(res, 500, false, 'Failed to get permissions');
  }
});

/**
 * @route GET /api/v1/auth/health
 * @desc Authentication service health check
 * @access Public
 */
router.get('/health', async (req, res) => {
  try {
    const health = await authService.healthCheck();
    
    sendResponse(res, 200, true, 'Auth service health check', health);
  } catch (error) {
    console.error('Auth health check error:', error);
    sendResponse(res, 500, false, 'Auth service unhealthy');
  }
});

export default router;
