/**
 * Middleware to automatically inject clinic context into requests
 * This ensures all data operations are properly scoped to the current clinic
 */

import { sendResponse } from '../utils/responseHandler.js';

/**
 * Middleware that automatically adds clinicId to request body and query parameters
 * This ensures all data operations are clinic-scoped by default
 */
export const injectClinicContext = (options = {}) => {
    const {
        required = true,
        bodyField = 'clinicId',
        queryField = 'clinicId',
        skipRoutes = []
    } = options;

    return (req, res, next) => {
        // Skip for certain routes if specified
        if (skipRoutes.some(route => req.path.includes(route))) {
            return next();
        }

        // Get clinic ID from various sources
        let clinicId = null;

        // 1. From request body (highest priority)
        if (req.body && req.body[bodyField]) {
            clinicId = parseInt(req.body[bodyField]);
        }
        // 2. From query parameters
        else if (req.query && req.query[queryField]) {
            clinicId = parseInt(req.query[queryField]);
        }
        // 3. From user context (staff member's current clinic)
        else if (req.user && req.user.currentClinicId) {
            clinicId = parseInt(req.user.currentClinicId);
        }
        // 4. From user context (staff member's primary clinic)
        else if (req.user && req.user.primaryClinicId) {
            clinicId = parseInt(req.user.primaryClinicId);
        }
        // 5. From user context (staff member's clinic)
        else if (req.user && req.user.clinicId) {
            clinicId = parseInt(req.user.clinicId);
        }

        // If clinic ID is required but not found, return error
        if (required && !clinicId) {
            return sendResponse(res, 400, false, "Clinic context is required for this operation");
        }

        // Inject clinic ID into request body for POST/PUT operations
        if (clinicId && req.body && (req.method === 'POST' || req.method === 'PUT')) {
            req.body[bodyField] = clinicId;
        }

        // Inject clinic ID into query parameters for GET operations
        if (clinicId && (req.method === 'GET' || req.method === 'DELETE')) {
            req.query[queryField] = clinicId.toString();
        }

        // Store clinic ID in request for easy access
        req.clinicId = clinicId;

        next();
    };
};

/**
 * Middleware specifically for data creation endpoints
 * Ensures all created data is properly linked to a clinic
 */
export const requireClinicContext = injectClinicContext({
    required: true,
    skipRoutes: ['/auth/', '/admin/', '/system/']
});

/**
 * Middleware for optional clinic context
 * Adds clinic ID if available but doesn't require it
 */
export const optionalClinicContext = injectClinicContext({
    required: false
});

/**
 * Middleware to validate clinic access
 * Ensures the user has access to the specified clinic
 */
export const validateClinicAccess = async (req, res, next) => {
    try {
        const clinicId = req.clinicId || req.body.clinicId || req.query.clinicId;
        
        if (!clinicId) {
            return next(); // Skip validation if no clinic ID
        }

        const user = req.user;
        if (!user) {
            return sendResponse(res, 401, false, "Authentication required");
        }

        // Admin users have access to all clinics
        if (user.email === '<EMAIL>' || user.roleId === 1001 || user.roleName === 'super_admin') {
            console.log(`🔑 Admin clinic access granted for ${user.email || user.userId}`);
            return next();
        }

        // Check if user has access to this clinic
        const hasAccess = 
            user.currentClinicId === parseInt(clinicId) ||
            user.primaryClinicId === parseInt(clinicId) ||
            user.clinicId === parseInt(clinicId) ||
            (user.additionalClinics && user.additionalClinics.includes(parseInt(clinicId)));

        if (!hasAccess) {
            return sendResponse(res, 403, false, "Access denied to this clinic");
        }

        next();
    } catch (error) {
        console.error('Clinic access validation error:', error);
        return sendResponse(res, 500, false, "Error validating clinic access");
    }
};

/**
 * Combined middleware for secure clinic-scoped operations
 */
export const secureClinicOperation = [
    requireClinicContext,
    validateClinicAccess
];
