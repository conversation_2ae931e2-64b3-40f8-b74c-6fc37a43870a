import React, { useState, useEffect } from "react";
import {
  Building2, Home, Users, Calendar, ClipboardList, Package2, PawPrint,
  ChevronDown, ChevronRight, Shield, Cat, DollarSign, BarChart2, Bell, 
  FileText, Settings, Clipboard, MessageCircle, Activity, HeartPulse, 
  Microscope, Thermometer, Dna, X, Menu, Sparkles
} from "lucide-react";
import { NavLink, useLocation } from "react-router-dom";
import { useIsMobile } from "@/hooks/use-mobile";
import { motion, AnimatePresence } from "framer-motion";
import { cn } from "@/lib/utils";

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

const navigation = [
  { 
    name: "Dashboard", 
    to: "/dashboard", 
    icon: Home, 
    color: "from-blue-500 to-blue-600",
    description: "Overview & Analytics"
  },
  {
    name: "Clients & Pets",
    icon: Users,
    color: "from-green-500 to-green-600",
    description: "Manage clients and pets",
    items: [
      { name: "All Clients", to: "/clients", icon: Users },
      { name: "All Pets", to: "/pets", icon: PawPrint },
      { name: "Species", to: "/species", icon: Cat },
      { name: "Breeds", to: "/breeds", icon: Dna },
    ],
  },
  { 
    name: "Appointments", 
    to: "/appointments", 
    icon: Calendar,
    color: "from-purple-500 to-purple-600",
    description: "Schedule & manage appointments"
  },
  {
    name: "Medical Records",
    icon: ClipboardList,
    color: "from-red-500 to-red-600",
    description: "Health records & treatments",
    items: [
      { name: "All Records", to: "/medical-records", icon: Clipboard },
      { name: "Consultations", to: "/records/consultations", icon: Activity },
      { name: "Vaccinations", to: "/records/vaccinations", icon: HeartPulse },
      { name: "Laboratory", to: "/records/laboratory", icon: Microscope },
    ],
  },
  {
    name: "Billing & Finance",
    icon: DollarSign,
    color: "from-yellow-500 to-yellow-600",
    description: "Invoices & payments",
    items: [
      { name: "Invoices", to: "/invoices", icon: FileText },
      { name: "Receipts", to: "/receipts", icon: FileText },
      { name: "Reports", to: "/billing/reports", icon: BarChart2 },
    ],
  },
  { 
    name: "Inventory", 
    to: "/inventory", 
    icon: Package2,
    color: "from-orange-500 to-orange-600",
    description: "Stock & supplies management"
  },
  {
    name: "Administration",
    icon: Shield,
    color: "from-gray-500 to-gray-600",
    description: "System settings & users",
    items: [
      { name: "Clinics", to: "/admin/clinics", icon: Building2 },
      { name: "Staff", to: "/admin/staff", icon: Users },
      { name: "Roles", to: "/admin/roles", icon: Shield },
      { name: "Settings", to: "/admin/settings", icon: Settings },
    ],
  },
];

export const EnhancedSidebar: React.FC<SidebarProps> = ({ isOpen, onClose }) => {
  const [expandedItems, setExpandedItems] = useState<string[]>([]);
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);
  const location = useLocation();
  const isMobile = useIsMobile();

  const toggleExpanded = (itemName: string) => {
    setExpandedItems(prev =>
      prev.includes(itemName)
        ? prev.filter(name => name !== itemName)
        : [...prev, itemName]
    );
  };

  // Auto-expand active sections
  useEffect(() => {
    navigation.forEach(item => {
      if (item.items) {
        const hasActiveChild = item.items.some(child => 
          location.pathname.startsWith(child.to)
        );
        if (hasActiveChild && !expandedItems.includes(item.name)) {
          setExpandedItems(prev => [...prev, item.name]);
        }
      }
    });
  }, [location.pathname]);

  const sidebarVariants = {
    open: {
      width: isOpen ? (isMobile ? "100%" : "280px") : "64px",
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30
      }
    },
    closed: {
      width: "64px",
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30
      }
    }
  };

  const contentVariants = {
    open: {
      opacity: 1,
      x: 0,
      transition: {
        delay: 0.1,
        duration: 0.3
      }
    },
    closed: {
      opacity: 0,
      x: -20,
      transition: {
        duration: 0.2
      }
    }
  };

  return (
    <>
      {/* Mobile Overlay */}
      <AnimatePresence>
        {isMobile && isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 z-40 lg:hidden"
            onClick={onClose}
          />
        )}
      </AnimatePresence>

      {/* Sidebar */}
      <motion.aside
        variants={sidebarVariants}
        animate={isOpen ? "open" : "closed"}
        className={cn(
          "fixed left-0 top-16 h-[calc(100vh-4rem)] bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl border-r border-gray-200 dark:border-gray-700 z-50 overflow-hidden",
          "shadow-xl"
        )}
      >
        {/* Gradient Background */}
        <div className="absolute inset-0 bg-gradient-to-b from-blue-50/50 to-purple-50/50 dark:from-blue-900/20 dark:to-purple-900/20" />
        
        {/* Content */}
        <div className="relative h-full flex flex-col">
          {/* Header */}
          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
            <AnimatePresence>
              {isOpen && (
                <motion.div
                  variants={contentVariants}
                  initial="closed"
                  animate="open"
                  exit="closed"
                  className="flex items-center justify-between"
                >
                  <div className="flex items-center gap-3">
                    <div className="p-2 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600">
                      <Sparkles className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h2 className="font-semibold text-gray-900 dark:text-gray-100">
                        VetCare Pro
                      </h2>
                      <p className="text-xs text-gray-500">
                        Practice Management
                      </p>
                    </div>
                  </div>
                  {isMobile && (
                    <button
                      onClick={onClose}
                      className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  )}
                </motion.div>
              )}
            </AnimatePresence>
            
            {!isOpen && (
              <div className="flex justify-center">
                <div className="p-2 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600">
                  <Sparkles className="h-5 w-5 text-white" />
                </div>
              </div>
            )}
          </div>

          {/* Navigation */}
          <nav className="flex-1 p-4 space-y-2 overflow-y-auto">
            {navigation.map((item, index) => (
              <motion.div
                key={item.name}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.05 }}
                onMouseEnter={() => setHoveredItem(item.name)}
                onMouseLeave={() => setHoveredItem(null)}
              >
                {item.to ? (
                  // Single item
                  <NavLink
                    to={item.to}
                    className={({ isActive }) =>
                      cn(
                        "flex items-center gap-3 px-3 py-3 rounded-xl transition-all duration-200 group relative overflow-hidden",
                        isActive
                          ? "bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg"
                          : "hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-700 dark:text-gray-300"
                      )
                    }
                  >
                    {({ isActive }) => (
                      <>
                        <div className={cn(
                          "p-2 rounded-lg transition-all duration-200",
                          isActive 
                            ? "bg-white/20" 
                            : `bg-gradient-to-br ${item.color} text-white group-hover:scale-110`
                        )}>
                          <item.icon className="h-4 w-4" />
                        </div>
                        
                        <AnimatePresence>
                          {isOpen && (
                            <motion.div
                              variants={contentVariants}
                              initial="closed"
                              animate="open"
                              exit="closed"
                              className="flex-1 min-w-0"
                            >
                              <div className="font-medium">{item.name}</div>
                              {item.description && (
                                <div className="text-xs opacity-75 truncate">
                                  {item.description}
                                </div>
                              )}
                            </motion.div>
                          )}
                        </AnimatePresence>
                      </>
                    )}
                  </NavLink>
                ) : (
                  // Expandable item
                  <div>
                    <button
                      onClick={() => toggleExpanded(item.name)}
                      className="w-full flex items-center gap-3 px-3 py-3 rounded-xl transition-all duration-200 group hover:bg-gray-100 dark:hover:bg-gray-800 text-gray-700 dark:text-gray-300"
                    >
                      <div className={`p-2 rounded-lg bg-gradient-to-br ${item.color} text-white group-hover:scale-110 transition-transform duration-200`}>
                        <item.icon className="h-4 w-4" />
                      </div>
                      
                      <AnimatePresence>
                        {isOpen && (
                          <motion.div
                            variants={contentVariants}
                            initial="closed"
                            animate="open"
                            exit="closed"
                            className="flex-1 min-w-0 text-left"
                          >
                            <div className="font-medium">{item.name}</div>
                            {item.description && (
                              <div className="text-xs opacity-75 truncate">
                                {item.description}
                              </div>
                            )}
                          </motion.div>
                        )}
                      </AnimatePresence>
                      
                      {isOpen && (
                        <motion.div
                          animate={{ 
                            rotate: expandedItems.includes(item.name) ? 90 : 0 
                          }}
                          transition={{ duration: 0.2 }}
                        >
                          <ChevronRight className="h-4 w-4" />
                        </motion.div>
                      )}
                    </button>

                    {/* Submenu */}
                    <AnimatePresence>
                      {expandedItems.includes(item.name) && isOpen && (
                        <motion.div
                          initial={{ height: 0, opacity: 0 }}
                          animate={{ height: "auto", opacity: 1 }}
                          exit={{ height: 0, opacity: 0 }}
                          transition={{ duration: 0.3 }}
                          className="ml-6 mt-2 space-y-1 overflow-hidden"
                        >
                          {item.items?.map((subItem) => (
                            <NavLink
                              key={subItem.name}
                              to={subItem.to}
                              className={({ isActive }) =>
                                cn(
                                  "flex items-center gap-3 px-3 py-2 rounded-lg transition-all duration-200 text-sm",
                                  isActive
                                    ? "bg-blue-50 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 border-l-2 border-blue-500"
                                    : "hover:bg-gray-50 dark:hover:bg-gray-800/50 text-gray-600 dark:text-gray-400"
                                )
                              }
                            >
                              <subItem.icon className="h-3 w-3" />
                              <span>{subItem.name}</span>
                            </NavLink>
                          ))}
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </div>
                )}
              </motion.div>
            ))}
          </nav>

          {/* Footer */}
          <div className="p-4 border-t border-gray-200 dark:border-gray-700">
            <AnimatePresence>
              {isOpen && (
                <motion.div
                  variants={contentVariants}
                  initial="closed"
                  animate="open"
                  exit="closed"
                  className="text-center"
                >
                  <div className="text-xs text-gray-500">
                    VetCare Pro v2.0
                  </div>
                  <div className="text-xs text-gray-400">
                    © 2024 VetCare Systems
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </div>
      </motion.aside>
    </>
  );
};
