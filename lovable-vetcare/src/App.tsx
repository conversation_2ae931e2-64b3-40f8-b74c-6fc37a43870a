
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { useAuth } from "./store";
import { ThemeProvider } from "next-themes";
import Layout from "./components/layout/Layout";
import Landing from "./pages/landing/Landing";
import RegisterOwnerAndClinic from "./pages/auth/RegisterOwnerAndClinic";
import Login from "./pages/auth/Login";
import Index from "./pages/Index";
import Clients from "./pages/clients/Clients";
import AddClient from "./pages/clients/AddClient";
import EditClient from "./pages/clients/EditClient";
import ClientProfile from "./pages/clients/ClientProfile";
import ClientPets from "@/pages/clients/ClientPets.tsx";
import Appointments from "./pages/appointments/Appointments";
import Species from "./pages/animals/Species";
import Breeds from "./pages/animals/Breeds";
import Inventory from "./pages/inventory/Inventory";
import Consultations from "./pages/medical-records/Consultations";
import Vaccinations from "./pages/medical-records/Vaccinations";
import Surgeries from "./pages/medical-records/Surgeries";
import Laboratory from "./pages/medical-records/Laboratory";
import ViewHealthRecord from "./pages/medical-records/ViewHealthRecord";
import Clinics from "./pages/admin/clinics/Clinics.tsx";
import AddClinic from "./pages/admin/clinics/AddClinic.tsx";
import EditClinic from "./pages/admin/clinics/EditClinic.tsx";
import ViewClinic from "./pages/admin/clinics/ViewClinic.tsx";
import Staff from "./pages/admin/staff/Staff.tsx";
import AddStaff from "./pages/admin/staff/AddStaff.tsx";
import EditStaff from "./pages/admin/staff/EditStaff.tsx";
import StaffView from "./pages/admin/staff/StaffView.tsx";
import EditStaffPermissions from "./pages/admin/staff/EditStaffPermissions.tsx";
import AddRole from "./pages/admin/roles/AddRole.tsx";
import Roles from "./pages/admin/roles/Roles.tsx";
import EditRole from "./pages/admin/roles/EditRole.tsx";
import Permissions from "./pages/admin/permissions/Permissions.tsx";
import AddPermission from "./pages/admin/permissions/AddPermission.tsx";
// import EditPermission from "./pages/admin/permissions/EditPermission.tsx";
import ViewProfile from "./pages/profile/ViewProfile.tsx";
import NotFound from "./pages/NotFound";
import AddAppointment from "./pages/appointments/AddAppointment.tsx";
import Settings from "./pages/settings/Settings";
import UnifiedPets from "./pages/pets/UnifiedPets";
import MedicalRecords from "./pages/medical-records/MedicalRecords";
import Invoices from "./pages/invoices/Invoices";
import AppointmentDetails from "./pages/appointments/AppointmentDetails.tsx";
import StartAppointment from "./pages/appointments/StartAppointment.tsx";

import InvoiceDetails from "./pages/invoices/InvoiceDetails.tsx";
import ReceiptDetails from "./pages/receipts/ReceiptDetails.tsx";
import GenerateReceipt from "./pages/receipts/GenerateReceipt.tsx";
import MedicalHistory from "./pages/pets/MedicalHistory.tsx";
import WaitingRoom from "./pages/patient-care/WaitingRoom";
import AdminSettings from "./pages/admin/Settings";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000,
      retry: (failureCount, error: any) => {
        // Don't retry on 401 unauthorized errors
        if (error?.status === 401 || error?.response?.status === 401) {
          return false;
        }
        // Otherwise retry up to 3 times
        return failureCount < 3;
      },
      retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
    },
  },
});

const PrivateRoute = ({ children }: { children: React.ReactNode }) => {
  const { isAuthenticated } = useAuth();
  return isAuthenticated ? children : <Navigate to="/login" />;
};

const App = () => {
  const { isAuthenticated } = useAuth();

  return (
    <ThemeProvider attribute="class" defaultTheme="system" enableSystem>
      <QueryClientProvider client={queryClient}>
        <TooltipProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
            <Routes>
              <Route path="/" element={isAuthenticated ? <Navigate to="/dashboard" /> : <Landing />} />
              <Route path="/register/owner" element={<RegisterOwnerAndClinic />} />
              <Route path="/login" element={isAuthenticated ? <Navigate to="/dashboard" /> : <Login />} />

              <Route path="/" element={<PrivateRoute><Layout /></PrivateRoute>}>
                <Route path="dashboard" element={<Index />} />

                <Route path="species" element={<Species />} />
                <Route path="breeds" element={<Breeds />} />

                <Route path="clients" element={<Clients />} />
                <Route path="clients/add" element={<AddClient />} />
                <Route path="clients/pets" element={<ClientPets />} />
                <Route path="clients/:id" element={<ClientProfile />} />
                <Route path="clients/:id/edit" element={<EditClient />} />
                <Route path="clients/:id/pets" element={<UnifiedPets />} />
                <Route path="communications" element={<NotFound />} />

                <Route path="admin/clinics" element={<Clinics />} />
                <Route path="admin/clinics/add" element={<AddClinic />} />
                <Route path="admin/clinics/edit/:id" element={<EditClinic />} />
                <Route path="admin/clinics/view/:clinicId" element={<ViewClinic />} />
                <Route path="profile" element={<ViewProfile />} />

                <Route path="admin/staff" element={<Staff />} />
                <Route path="admin/staff/:_id" element={<StaffView />} />
                <Route path="admin/staff/:_id/edit" element={<EditStaff />} />
                <Route path="admin/staff/:_id/permissions" element={<EditStaffPermissions />} />

                <Route path="admin/roles" element={<Roles />} />
                <Route path="admin/roles/add" element={<AddRole />} />
                <Route path="admin/roles/edit/:id" element={<EditRole />} />

                <Route path="admin/permissions" element={<Permissions />} />
                <Route path="admin/permissions/add" element={<AddPermission />} />
                {/* <Route path="admin/permissions/edit/:id" element={<EditPermission />} /> */}

                <Route path="admin/settings" element={<AdminSettings />} />

                <Route path="settings" element={<Settings />} />
                <Route path="settings/user" element={<Settings />} />

                <Route path="service-types" element={<NotFound />} />
                <Route path="admin/schedules" element={<NotFound />} />
                <Route path="admin/reminders" element={<NotFound />} />
                <Route path="admin/templates" element={<NotFound />} />

                <Route path="pets" element={<UnifiedPets />} />

                <Route path="pets/:petId/medical-history" element={<MedicalHistory />} />

                <Route path="appointments" element={<Appointments />} />
                <Route path="appointments/add" element={<AddAppointment />} />
                <Route path="appointments/:id" element={<AppointmentDetails />} />

                <Route path="appointments/:id/start" element={<StartAppointment />} />

                <Route path="medical-records" element={<MedicalRecords />} />
                <Route path="invoices" element={<Invoices />} />
                <Route path="receipts" element={<Invoices />} />

                <Route path="invoices/appointment/:appointmentId" element={<InvoiceDetails />} />
                <Route path="receipts/:receiptId" element={<ReceiptDetails />} />
                <Route path="receipts/appointment/:appointmentId" element={<GenerateReceipt />} />

                <Route path="waiting-room" element={<WaitingRoom />} />
                <Route path="patients-queue" element={<NotFound />} />

                <Route path="records/consultations" element={<Consultations />} />
                <Route path="records/vaccinations" element={<Vaccinations />} />
                <Route path="records/surgeries" element={<Surgeries />} />
                <Route path="records/laboratory" element={<Laboratory />} />
                <Route path="records/diagnostics" element={<NotFound />} />
                <Route path="records/vitals" element={<NotFound />} />
                <Route path="records/:recordId" element={<ViewHealthRecord />} />

                <Route path="inventory" element={<Inventory />} />
                <Route path="inventory/medications" element={<NotFound />} />
                <Route path="inventory/supplies" element={<NotFound />} />
                <Route path="inventory/orders" element={<NotFound />} />

                <Route path="billing" element={<NotFound />} />
                <Route path="billing/invoices" element={<NotFound />} />
                <Route path="billing/payments" element={<NotFound />} />
                <Route path="billing/reports" element={<NotFound />} />

                <Route path="*" element={<NotFound />} />
              </Route>
            </Routes>
          </BrowserRouter>
        </TooltipProvider>
      </QueryClientProvider>
    </ThemeProvider>
  );
};

export default App;
